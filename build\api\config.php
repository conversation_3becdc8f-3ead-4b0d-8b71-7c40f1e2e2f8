<?php
// CORS ayarları
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

// OPTIONS request için
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Hub Veritabanı ayarları - Aynı sunucuda localhost bağlantısı
define('DB_HOST', 'localhost'); // Aynı sunucuda localhost
define('DB_NAME', 'metaanalizgroup_meta'); // Hub veritabanı
define('DB_USER', 'metaanalizgroup_gr');
define('DB_PASS', 'Mah2025!');

// Site configuration
define('SITE_CODE', 'metaanaliz-musavirlik');
define('SITE_NAME', 'Meta Analiz Müşavirlik');
define('SITE_URL', 'https://metaanalizmusavirlik.com');
define('ADMIN_EMAIL', '<EMAIL>');
define('NOREPLY_EMAIL', '<EMAIL>');

// E-posta ayarları
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_email_password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Meta Analiz Müşavirlik');
define('TO_EMAIL', '<EMAIL>');

// Güvenlik ayarları
define('API_KEY', 'meta_analiz_2024_secure_key');

// Hata raporlama
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Yardımcı fonksiyonlar
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validatePhone($phone) {
    // Türkiye telefon numarası formatı kontrolü
    $pattern = '/^(\+90|0)?[5][0-9]{9}$/';
    return preg_match($pattern, $phone);
}

function sendResponse($success, $message, $data = null) {
    // HTTP status kodunu ayarla
    http_response_code($success ? 200 : 400);

    // Content-Type header'ını ayarla
    header('Content-Type: application/json; charset=utf-8');

    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];

    if ($data !== null) {
        $response['data'] = $data;
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit();
}

// Hub Database connection function
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        http_response_code(500);
        sendResponse(false, 'Veritabanı bağlantı hatası: ' . $e->getMessage());
    }
}

// Get site ID by site code
function getSiteId($pdo, $siteCode) {
    $stmt = $pdo->prepare("SELECT id FROM sites WHERE site_code = ? AND is_active = 1");
    $stmt->execute([$siteCode]);
    $result = $stmt->fetch();
    return $result ? $result['id'] : null;
}

// Create notification
function createNotification($pdo, $siteId, $type, $referenceId, $title, $message) {
    $stmt = $pdo->prepare("
        INSERT INTO notifications (site_code, type, reference_id, title, message, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
    ");
    return $stmt->execute([$siteId, $type, $referenceId, $title, $message]);
}

function logRequest($type, $data) {
    $logFile = __DIR__ . '/logs/' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);

    if (!is_dir($logDir)) {
        @mkdir($logDir, 0755, true);
    }

    // Eğer klasör oluşturulamazsa sessizce geç
    if (!is_dir($logDir)) {
        return false;
    }

    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $type,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'data' => $data
    ];

    @file_put_contents($logFile, json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
}
