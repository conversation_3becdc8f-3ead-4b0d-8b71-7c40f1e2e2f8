(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[946],{7793:(e,l,i)=>{"use strict";i.r(l),i.d(l,{default:()=>m});var a=i(5155),s=i(7864),r=i(5922),t=i(6874),n=i.n(t);function m(){return(0,a.jsxs)("main",{className:"min-h-screen",children:[(0,a.jsx)(s.default,{currentPage:"hizmetlerimiz"}),(0,a.jsx)("section",{className:"pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"text-center text-white",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"Emeklilik Uzmanı"]}),(0,a.jsx)("h1",{className:"text-4xl md:text-6xl font-light mb-6 text-white",children:"Emeklilik Hizmetleri"}),(0,a.jsx)("p",{className:"text-xl text-white/90 max-w-3xl mx-auto",children:"Emeklilik hesaplamaları, bor\xe7lanma işlemleri ve emeklilik haklarınızla ilgili t\xfcm danışmanlık hizmetleri"})]})})}),(0,a.jsx)("section",{className:"py-24 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 p-4 md:p-8 rounded-2xl aspect-square flex flex-col",children:[(0,a.jsx)("div",{className:"w-12 h-12 md:w-16 md:h-16 bg-blue-600 rounded-xl flex items-center justify-center mb-4 md:mb-6 mx-auto",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Emeklilik Hesaplamaları"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Mevcut durumunuz \xfczerinden detaylı emeklilik tahminleri ve hesaplamaları yapıyoruz"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Emeklilik yaşı hesaplama"}),(0,a.jsx)("li",{children:"• Emekli maaşı tahmini"}),(0,a.jsx)("li",{children:"• Prim g\xfcn sayısı kontrol\xfc"}),(0,a.jsx)("li",{children:"• Emeklilik şartları analizi"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mb-6",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Bor\xe7lanma İşlemleri"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Askerlik, \xf6ğrencilik ve diğer bor\xe7lanma t\xfcrleri i\xe7in hesaplama ve işlem takibi"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Askerlik bor\xe7lanması"}),(0,a.jsx)("li",{children:"• \xd6ğrencilik bor\xe7lanması"}),(0,a.jsx)("li",{children:"• Eksik hizmet bor\xe7lanması"}),(0,a.jsx)("li",{children:"• Bor\xe7lanma maliyeti hesaplama"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Hak Analizi"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Emeklilik haklarınızın detaylı analizi ve optimizasyon \xf6nerileri"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Emeklilik hak analizi"}),(0,a.jsx)("li",{children:"• Ek \xf6deme hakları"}),(0,a.jsx)("li",{children:"• Yaşlılık aylığı hesaplama"}),(0,a.jsx)("li",{children:"• Malull\xfck analizi"})]})]})]})})})}),(0,a.jsx)("section",{className:"py-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"text-center text-white",children:[(0,a.jsx)("h3",{className:"text-3xl md:text-4xl font-light mb-6",children:"Emeklilik S\xfcrecinizde Yanınızdayız"}),(0,a.jsx)("p",{className:"text-xl text-white/90 mb-8 max-w-2xl mx-auto",children:"Uzman kadromuzla emeklilik haklarınızı en iyi şekilde değerlendirin"}),(0,a.jsxs)(n(),{href:"/iletisim",className:"inline-flex items-center px-8 py-4 bg-white text-slate-900 font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg",children:["İletişime Ge\xe7in",(0,a.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]})})}),(0,a.jsx)(r.A,{})]})}},8783:(e,l,i)=>{Promise.resolve().then(i.bind(i,7793))}},e=>{var l=l=>e(e.s=l);e.O(0,[874,766,49,441,684,358],()=>l(8783)),_N_E=e.O()}]);