# Next.js Static Export için URL Rewriting
RewriteEngine On

# MIME Types
AddType text/css .css
AddType application/javascript .js
AddType image/webp .webp
AddType image/svg+xml .svg

# CORS Headers for API
<FilesMatch "\.(php)$">
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</FilesMatch>

# API isteklerini PHP dosyalarına yönlendir
RewriteRule ^api/contact$ api/contact.php [L]
RewriteRule ^api/meeting$ api/meeting.php [L]

# Static dosyalar için cache headers
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|ico)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
    Header set Cache-Control "public, max-age=31536000"
    Header unset ETag
    FileETag None
</FilesMatch>

# CSS ve JS dosyaları için özel ayarlar
<FilesMatch "\.css$">
    Header set Content-Type "text/css; charset=utf-8"
    Header set Access-Control-Allow-Origin "*"
    Header set Cache-Control "no-cache, must-revalidate"
    Header set Pragma "no-cache"
</FilesMatch>

<FilesMatch "\.js$">
    Header set Content-Type "application/javascript; charset=utf-8"
    Header set Access-Control-Allow-Origin "*"
</FilesMatch>

# HTML dosyaları için
<FilesMatch "\.html$">
    ExpiresActive On
    ExpiresDefault "access plus 1 day"
</FilesMatch>

# Güvenlik headers
Header always set X-Frame-Options "DENY"
Header always set X-Content-Type-Options "nosniff"
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# HTTPS yönlendirmesi (opsiyonel)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
