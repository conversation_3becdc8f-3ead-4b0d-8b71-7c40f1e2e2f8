(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[925],{295:(e,a,r)=>{Promise.resolve().then(r.bind(r,5202))},5202:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>i});var l=r(5155),s=r(2115),t=r(7864),n=r(5922);function i(){let[e,a]=(0,s.useState)({name:"",email:"",phone:"",company:"",service:"",preferred_date:"",preferred_time:"",meeting_type:"office",notes:""}),[r,i]=(0,s.useState)(!1),[m,o]=(0,s.useState)(""),[d,c]=(0,s.useState)(!1),u=r=>{a({...e,[r.target.name]:r.target.value})},x=async r=>{r.preventDefault(),i(!0),o(""),c(!1);try{let r=await fetch("/api/meeting.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),t=await r.json();if(console.log("Meeting API Response:",t),r.ok&&t.success)c(!0),o("Toplantı talebiniz başarıyla g\xf6nderildi! En kısa s\xfcrede size d\xf6n\xfcş yapacağız."),a({name:"",email:"",phone:"",company:"",service:"",preferred_date:"",preferred_time:"",meeting_type:"office",notes:""}),setTimeout(()=>{o(""),c(!1)},5e3);else{var l,s;c(!1),o("Hata: "+(null!=(s=null!=(l=t.message)?l:t.error)?s:"Bilinmeyen bir hata oluştu"))}}catch(e){console.error("Meeting form error:",e),c(!1),o("Bir hata oluştu. L\xfctfen tekrar deneyin.")}finally{i(!1)}},p=new Date().toISOString().split("T")[0];return(0,l.jsxs)("main",{className:"min-h-screen",children:[(0,l.jsx)(t.default,{currentPage:"toplanti-planla"}),(0,l.jsx)("section",{className:"pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,l.jsx)("div",{className:"container mx-auto px-6",children:(0,l.jsxs)("div",{className:"text-center text-white",children:[(0,l.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20",children:[(0,l.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"\xdccretsiz Danışmanlık/M\xfcşavirlik"]}),(0,l.jsx)("h1",{className:"text-4xl md:text-6xl font-light mb-6 text-white",children:"Toplantı Planla"}),(0,l.jsx)("p",{className:"text-xl text-white/90 max-w-3xl mx-auto",children:"Konusunda uzman danışman/m\xfcşavir ekibimizle g\xf6r\xfcşme planlamak i\xe7in formu doldurun"})]})})}),(0,l.jsx)("section",{className:"py-24 bg-white",children:(0,l.jsx)("div",{className:"container mx-auto px-6",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[m&&(0,l.jsx)("div",{className:"mb-8 p-4 rounded-lg transition-all duration-300 ".concat(d?"bg-green-50 text-green-800 border border-green-200 shadow-sm":"bg-red-50 text-red-800 border border-red-200 shadow-sm"),children:(0,l.jsxs)("div",{className:"flex items-center",children:[d?(0,l.jsx)("svg",{className:"w-5 h-5 mr-3 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):(0,l.jsx)("svg",{className:"w-5 h-5 mr-3 text-red-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,l.jsx)("span",{className:"font-medium",children:m})]})}),(0,l.jsxs)("form",{onSubmit:x,className:"space-y-4 sm:space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Ad Soyad *"}),(0,l.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:u,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base",placeholder:"Adınız ve soyadınız"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"E-posta *"}),(0,l.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:u,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base",placeholder:"<EMAIL>"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Telefon *"}),(0,l.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:u,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base",placeholder:"05XX XXX XX XX"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Şirket"}),(0,l.jsx)("input",{type:"text",id:"company",name:"company",value:e.company,onChange:u,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base",placeholder:"Şirket adınız"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"service",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Hizmet T\xfcr\xfc *"}),(0,l.jsxs)("select",{id:"service",name:"service",value:e.service,onChange:u,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 text-sm sm:text-base",children:[(0,l.jsx)("option",{value:"",className:"text-gray-500",children:"Hizmet se\xe7in"}),[{value:"insan-kaynaklari",label:"İnsan Kaynakları Hizmetleri"},{value:"emeklilik",label:"Emeklilik Hizmetleri"},{value:"kurumsal-danismanlik",label:"Kurumsal Danışmanlık"},{value:"sgk-uyusmazliklari",label:"SGK Uyuşmazlıkları"},{value:"is-sagligi-guvenligi",label:"İş Sağlığı ve G\xfcvenliği"},{value:"mali-musavirlik",label:"Mali M\xfcşavirlik"},{value:"genel-danismanlik",label:"Genel Danışmanlık"}].map(e=>(0,l.jsx)("option",{value:e.value,className:"text-gray-900",children:e.label},e.value))]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"meeting_type",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Toplantı T\xfcr\xfc *"}),(0,l.jsx)("select",{id:"meeting_type",name:"meeting_type",value:e.meeting_type,onChange:u,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 text-sm sm:text-base",children:[{value:"office",label:"Ofiste G\xf6r\xfcşme"},{value:"online",label:"Online G\xf6r\xfcşme"},{value:"phone",label:"Telefon G\xf6r\xfcşmesi"}].map(e=>(0,l.jsx)("option",{value:e.value,className:"text-gray-900",children:e.label},e.value))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"preferred_date",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Tercih Edilen Tarih *"}),(0,l.jsx)("input",{type:"date",id:"preferred_date",name:"preferred_date",value:e.preferred_date,onChange:u,min:p,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"preferred_time",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Tercih Edilen Saat *"}),(0,l.jsx)("input",{type:"time",id:"preferred_time",name:"preferred_time",value:e.preferred_time,onChange:u,min:"09:00",max:"18:00",required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base"}),(0,l.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"\xc7alışma saatleri: 09:00 - 18:00"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"notes",className:"block text-sm font-medium text-gray-700 mb-2",children:"Ek Notlar"}),(0,l.jsx)("textarea",{id:"notes",name:"notes",value:e.notes,onChange:u,rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Toplantı ile ilgili \xf6zel notlarınız varsa buraya yazabilirsiniz..."})]}),(0,l.jsx)("div",{className:"text-center",children:(0,l.jsxs)("button",{type:"submit",disabled:r,className:"glass-btn inline-flex items-center px-8 py-3 text-lg font-medium text-white transition-all duration-300 ".concat(r?"opacity-50 cursor-not-allowed":"hover:scale-105"),children:[(0,l.jsx)("span",{children:r?"G\xf6nderiliyor...":"Toplantı Talep Et"}),!r&&(0,l.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})]})})]})]})})}),(0,l.jsx)(n.A,{})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[874,766,49,441,684,358],()=>a(295)),_N_E=e.O()}]);