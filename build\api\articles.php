<?php
// Articles API for Meta Analiz Müşavirlik frontend

// CORS headers
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header("Content-Type: application/json");

// Handle preflight requests
if ($_SERVER["REQUEST_METHOD"] == "OPTIONS") {
    exit(0);
}

require_once __DIR__ . '/config.php';

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Only allow GET requests for public API
if ($method !== 'GET') {
    http_response_code(405);
    sendResponse(false, 'Method not allowed');
}

try {
    $pdo = getDBConnection();
    
    // Get URL parameters
    $slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';
    $limit = isset($_GET['limit']) ? min(50, max(1, (int)$_GET['limit'])) : 12;
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $offset = ($page - 1) * $limit;
    
    if ($slug) {
        // Get single article by slug
        getArticleBySlug($pdo, $slug);
    } else {
        // Get articles list
        getArticlesList($pdo, $limit, $offset, $page);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    sendResponse(false, 'Server error: ' . $e->getMessage());
}

// Get single article by slug
function getArticleBySlug($pdo, $slug) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                id, title, slug, excerpt, content, author, category,
                meta_title, meta_description, meta_keywords,
                published_at, created_at, updated_at
            FROM articles 
            WHERE slug = ? AND status = 'published' AND site_code = 'metaanaliz-musavirlik'
        ");
        
        $stmt->execute([$slug]);
        $article = $stmt->fetch();
        
        if (!$article) {
            http_response_code(404);
            sendResponse(false, 'Article not found');
            return;
        }
        
        // Format dates
        $article['published_at'] = $article['published_at'] ? 
            date('Y-m-d', strtotime($article['published_at'])) : null;
        $article['created_at'] = date('Y-m-d', strtotime($article['created_at']));
        
        // Add read time estimation (approximate)
        $wordCount = str_word_count(strip_tags($article['content']));
        $readTime = max(1, ceil($wordCount / 200)); // 200 words per minute
        $article['read_time'] = $readTime . ' dk okuma';
        
        sendResponse(true, 'Article retrieved successfully', $article);
        
    } catch (PDOException $e) {
        http_response_code(500);
        sendResponse(false, 'Database error: ' . $e->getMessage());
    }
}

// Get articles list
function getArticlesList($pdo, $limit, $offset, $page) {
    try {
        // Get total count
        $countStmt = $pdo->prepare("
            SELECT COUNT(*) as total 
            FROM articles 
            WHERE status = 'published' AND site_code = 'metaanaliz-musavirlik'
        ");
        $countStmt->execute();
        $total = $countStmt->fetch()['total'];
        
        // Get articles
        $stmt = $pdo->prepare("
            SELECT 
                id, title, slug, excerpt, author, category,
                published_at, created_at, is_featured
            FROM articles 
            WHERE status = 'published' AND site_code = 'metaanaliz-musavirlik'
            ORDER BY published_at DESC, created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $stmt->execute([$limit, $offset]);
        $articles = $stmt->fetchAll();
        
        // Format articles
        foreach ($articles as &$article) {
            // Format date
            $article['date'] = $article['published_at'] ? 
                date('Y-m-d', strtotime($article['published_at'])) : 
                date('Y-m-d', strtotime($article['created_at']));
            
            // Add read time estimation
            if ($article['excerpt']) {
                $wordCount = str_word_count($article['excerpt']) * 4; // Estimate full content
            } else {
                $wordCount = 800; // Default estimation
            }
            $readTime = max(1, ceil($wordCount / 200));
            $article['readTime'] = $readTime . ' dk okuma';
            
            // Add thumbnail (placeholder for now)
            $article['thumbnail'] = '/blog/' . $article['slug'] . '.jpg';
            
            // Clean up
            unset($article['published_at'], $article['created_at']);
        }
        
        sendResponse(true, 'Articles retrieved successfully', [
            'articles' => $articles,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit),
                'hasNext' => $page < ceil($total / $limit),
                'hasPrev' => $page > 1
            ]
        ]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        sendResponse(false, 'Database error: ' . $e->getMessage());
    }
}

// Get featured articles (for homepage or special sections)
function getFeaturedArticles($pdo, $limit = 3) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                id, title, slug, excerpt, author, category,
                published_at, created_at
            FROM articles 
            WHERE status = 'published' AND site_code = 'metaanaliz-musavirlik' AND is_featured = 1
            ORDER BY published_at DESC, created_at DESC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        $articles = $stmt->fetchAll();
        
        // Format articles
        foreach ($articles as &$article) {
            $article['date'] = $article['published_at'] ? 
                date('Y-m-d', strtotime($article['published_at'])) : 
                date('Y-m-d', strtotime($article['created_at']));
            
            $article['thumbnail'] = '/blog/' . $article['slug'] . '.jpg';
            
            unset($article['published_at'], $article['created_at']);
        }
        
        return $articles;
        
    } catch (PDOException $e) {
        return [];
    }
}

// Get related articles (by category, excluding current article)
function getRelatedArticles($pdo, $currentSlug, $category, $limit = 3) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                id, title, slug, excerpt, author, category,
                published_at, created_at
            FROM articles 
            WHERE status = 'published' AND site_code = 'metaanaliz-musavirlik'
                AND slug != ? AND category = ?
            ORDER BY published_at DESC, created_at DESC
            LIMIT ?
        ");
        
        $stmt->execute([$currentSlug, $category, $limit]);
        $articles = $stmt->fetchAll();
        
        // Format articles
        foreach ($articles as &$article) {
            $article['date'] = $article['published_at'] ? 
                date('Y-m-d', strtotime($article['published_at'])) : 
                date('Y-m-d', strtotime($article['created_at']));
            
            $article['thumbnail'] = '/blog/' . $article['slug'] . '.jpg';
            
            unset($article['published_at'], $article['created_at']);
        }
        
        return $articles;
        
    } catch (PDOException $e) {
        return [];
    }
}

// Get article navigation (previous/next)
function getArticleNavigation($pdo, $currentSlug) {
    try {
        // Get current article date
        $currentStmt = $pdo->prepare("
            SELECT published_at, created_at 
            FROM articles 
            WHERE slug = ? AND status = 'published' AND site_code = 'metaanaliz-musavirlik'
        ");
        $currentStmt->execute([$currentSlug]);
        $current = $currentStmt->fetch();
        
        if (!$current) {
            return ['previous' => null, 'next' => null];
        }
        
        $currentDate = $current['published_at'] ?: $current['created_at'];
        
        // Get previous article (older)
        $prevStmt = $pdo->prepare("
            SELECT title, slug 
            FROM articles 
            WHERE status = 'published' AND site_code = 'metaanaliz-musavirlik'
                AND (published_at < ? OR (published_at IS NULL AND created_at < ?))
            ORDER BY COALESCE(published_at, created_at) DESC
            LIMIT 1
        ");
        $prevStmt->execute([$currentDate, $currentDate]);
        $previous = $prevStmt->fetch();
        
        // Get next article (newer)
        $nextStmt = $pdo->prepare("
            SELECT title, slug 
            FROM articles 
            WHERE status = 'published' AND site_code = 'metaanaliz-musavirlik'
                AND (published_at > ? OR (published_at IS NULL AND created_at > ?))
            ORDER BY COALESCE(published_at, created_at) ASC
            LIMIT 1
        ");
        $nextStmt->execute([$currentDate, $currentDate]);
        $next = $nextStmt->fetch();
        
        return [
            'previous' => $previous ?: null,
            'next' => $next ?: null
        ];
        
    } catch (PDOException $e) {
        return ['previous' => null, 'next' => null];
    }
}

// Handle special endpoints
if (isset($_GET['action'])) {
    $action = sanitizeInput($_GET['action']);
    
    switch ($action) {
        case 'featured':
            $limit = isset($_GET['limit']) ? min(10, max(1, (int)$_GET['limit'])) : 3;
            $articles = getFeaturedArticles($pdo, $limit);
            sendResponse(true, 'Featured articles retrieved', ['articles' => $articles]);
            break;
            
        case 'related':
            $slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';
            $category = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';
            $limit = isset($_GET['limit']) ? min(10, max(1, (int)$_GET['limit'])) : 3;
            
            if ($slug && $category) {
                $articles = getRelatedArticles($pdo, $slug, $category, $limit);
                sendResponse(true, 'Related articles retrieved', ['articles' => $articles]);
            } else {
                sendResponse(false, 'Slug and category parameters required');
            }
            break;
            
        case 'navigation':
            $slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';
            
            if ($slug) {
                $navigation = getArticleNavigation($pdo, $slug);
                sendResponse(true, 'Article navigation retrieved', $navigation);
            } else {
                sendResponse(false, 'Slug parameter required');
            }
            break;
            
        default:
            sendResponse(false, 'Invalid action');
            break;
    }
}
?>
