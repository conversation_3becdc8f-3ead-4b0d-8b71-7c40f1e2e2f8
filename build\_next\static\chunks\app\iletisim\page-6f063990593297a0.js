(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[157],{3096:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var r=a(5155),l=a(2115),t=a(7864),i=a(5922);function n(){let[e,s]=(0,l.useState)({name:"",email:"",phone:"",company:"",subject:"",message:""}),[a,n]=(0,l.useState)(!1),[o,d]=(0,l.useState)(""),[c,m]=(0,l.useState)(!1),x=a=>{s({...e,[a.target.name]:a.target.value})},h=async a=>{a.preventDefault(),n(!0),d(""),m(!1);try{let a=await fetch("/api/contact.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await a.json();console.log("API Response:",r),a.ok&&r.success?(m(!0),d("Mesajınız başarıyla g\xf6nderildi! En kısa s\xfcrede size d\xf6n\xfcş yapacağız."),s({name:"",email:"",phone:"",company:"",subject:"",message:""}),setTimeout(()=>{d(""),m(!1)},5e3)):(m(!1),d("Hata: "+(r.message||r.error||"Bilinmeyen bir hata oluştu")))}catch(e){console.error("Contact form error:",e),m(!1),d("Bir hata oluştu. L\xfctfen tekrar deneyin.")}finally{n(!1)}};return(0,r.jsxs)("main",{className:"min-h-screen",children:[(0,r.jsx)(t.default,{currentPage:"iletisim"}),(0,r.jsx)("section",{className:"pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,r.jsx)("div",{className:"container mx-auto px-6",children:(0,r.jsxs)("div",{className:"text-center text-white",children:[(0,r.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"7/24 Destek"]}),(0,r.jsx)("h1",{className:"text-4xl md:text-6xl font-light mb-6 text-white",children:"İletişim"}),(0,r.jsx)("p",{className:"text-xl text-white/90 max-w-3xl mx-auto",children:"Sorularınız i\xe7in bizimle iletişime ge\xe7in, size en kısa s\xfcrede d\xf6n\xfcş yapalım"})]})})}),(0,r.jsx)("section",{className:"py-24 bg-white",children:(0,r.jsx)("div",{className:"container mx-auto px-6",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,r.jsxs)("div",{className:"order-1 lg:order-2",children:[(0,r.jsx)("h2",{className:"text-3xl font-light mb-8 text-gray-900",children:"Mesaj G\xf6nderin"}),o&&(0,r.jsx)("div",{className:"mb-6 p-4 rounded-lg transition-all duration-300 ".concat(c?"bg-green-50 text-green-800 border border-green-200 shadow-sm":"bg-red-50 text-red-800 border border-red-200 shadow-sm"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[c?(0,r.jsx)("svg",{className:"w-5 h-5 mr-3 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):(0,r.jsx)("svg",{className:"w-5 h-5 mr-3 text-red-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,r.jsx)("span",{className:"font-medium",children:o})]})}),(0,r.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Ad Soyad *"}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:x,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",placeholder:"Adınız ve soyadınız"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"E-posta *"}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:x,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefon *"}),(0,r.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:x,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",placeholder:"0 (5XX) XXX XX XX"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-2",children:"Şirket"}),(0,r.jsx)("input",{type:"text",id:"company",name:"company",value:e.company,onChange:x,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",placeholder:"Şirket adınız"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Konu *"}),(0,r.jsxs)("select",{id:"subject",name:"subject",value:e.subject,onChange:x,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",children:[(0,r.jsx)("option",{value:"",children:"Konu se\xe7iniz"}),(0,r.jsx)("option",{value:"mali-musavirlik",children:"Mali M\xfcşavirlik"}),(0,r.jsx)("option",{value:"insan-kaynaklari",children:"İnsan Kaynakları"}),(0,r.jsx)("option",{value:"sgk-danismanlik",children:"SGK Danışmanlığı"}),(0,r.jsx)("option",{value:"is-sagligi-guvenligi",children:"İş Sağlığı ve G\xfcvenliği"}),(0,r.jsx)("option",{value:"emeklilik",children:"Emeklilik Hizmetleri"}),(0,r.jsx)("option",{value:"kurumsal-danismanlik",children:"Kurumsal Danışmanlık"}),(0,r.jsx)("option",{value:"diger",children:"Diğer"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Mesajınız *"}),(0,r.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:x,required:!0,rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none text-gray-900 bg-white",placeholder:"Mesajınızı buraya yazın..."})]}),(0,r.jsxs)("button",{type:"submit",disabled:a,className:"glass-btn w-full inline-flex items-center justify-center px-8 py-3 text-lg font-medium text-white transition-all duration-300 ".concat(a?"opacity-50 cursor-not-allowed":"hover:scale-105"),children:[(0,r.jsx)("span",{children:a?"G\xf6nderiliyor...":"Mesaj G\xf6nder"}),!a&&(0,r.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})]})]})]}),(0,r.jsxs)("div",{className:"order-2 lg:order-1",children:[(0,r.jsx)("h2",{className:"text-3xl font-light mb-8 text-gray-900",children:"İletişim Bilgileri"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,r.jsxs)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Adres"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Yenicami Mah. \xd6zmen Sok. No: 24/A",(0,r.jsx)("br",{}),"S\xf6ke Aydın"]})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Telefon"}),(0,r.jsx)("p",{className:"text-gray-600",children:"0 (542) 797 05 00 - 0 (542) 380 00 50"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"E-posta"}),(0,r.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"}),(0,r.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"\xc7alışma Saatleri"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Pazartesi - Cuma: 09:00 - 18:00",(0,r.jsx)("br",{}),"Cumartesi: 09:00 - 13:00"]})]})]})]})]})]})})})}),(0,r.jsx)("section",{className:"py-0",children:(0,r.jsx)("div",{className:"w-full h-96",children:(0,r.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3141.234567890123!2d27.3123456!3d37.7543210!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14bfd3e123456789%3A0x123456789abcdef!2sYenicami%20Mah.%20%C3%96zmen%20Sok.%20No%3A24%2FA%2C%2009270%20S%C3%B6ke%2FAyd%C4%B1n!5e0!3m2!1str!2str!4v1234567890123!5m2!1str!2str",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"Meta Analiz M\xfcşavirlik Ofis Konumu - S\xf6ke, Aydın"})})}),(0,r.jsx)(i.A,{})]})}},7605:(e,s,a)=>{Promise.resolve().then(a.bind(a,3096))}},e=>{var s=s=>e(e.s=s);e.O(0,[874,766,49,441,684,358],()=>s(7605)),_N_E=e.O()}]);