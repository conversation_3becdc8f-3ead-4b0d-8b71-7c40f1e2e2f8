<?php
// CORS headers - daha kapsamlı
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Max-Age: 86400');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS request için hızlı yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config.php';

// Debug için log
error_log("Contact API called - Method: " . $_SERVER['REQUEST_METHOD']);

// Sadece POST isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Sadece POST istekleri kabul edilir');
}

// JSON verilerini al
$rawInput = file_get_contents('php://input');
error_log("Raw input: " . $rawInput);

$input = json_decode($rawInput, true);
error_log("Decoded input: " . print_r($input, true));

// JSON decode hatası kontrolü
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("JSON decode error: " . json_last_error_msg());
    sendResponse(false, 'Geçersiz JSON formatı: ' . json_last_error_msg());
}

if ($input === null) {
    error_log("Input is null after JSON decode");
    sendResponse(false, 'Veri alınamadı');
}

// Gerekli alanları kontrol et (subject opsiyonel)
$requiredFields = ['name', 'email', 'message'];
$missingFields = [];

foreach ($requiredFields as $field) {
    if (!isset($input[$field]) || empty(trim($input[$field]))) {
        $missingFields[] = $field;
    }
}

if (!empty($missingFields)) {
    sendResponse(false, 'Eksik alanlar: ' . implode(', ', $missingFields));
}

// Verileri temizle
$name = sanitizeInput($input['name']);
$email = sanitizeInput($input['email']);
$phone = sanitizeInput($input['phone']);
$subject = sanitizeInput($input['subject']);
$message = sanitizeInput($input['message']);
$company = isset($input['company']) ? sanitizeInput($input['company']) : '';

// E-posta doğrulama
if (!validateEmail($email)) {
    sendResponse(false, 'Geçersiz e-posta adresi');
}

// Telefon doğrulama (opsiyonel)
if (!empty($phone) && !validatePhone($phone)) {
    sendResponse(false, 'Geçersiz telefon numarası formatı');
}

// İsim uzunluk kontrolü
if (strlen($name) < 2 || strlen($name) > 100) {
    sendResponse(false, 'İsim 2-100 karakter arasında olmalıdır');
}

// Mesaj uzunluk kontrolü
if (strlen($message) < 10 || strlen($message) > 1000) {
    sendResponse(false, 'Mesaj 10-1000 karakter arasında olmalıdır');
}

try {
    // Hub veritabanına bağlan
    $pdo = getDBConnection();

    // Site ID'sini al
    $siteId = getSiteId($pdo, SITE_CODE);
    if (!$siteId) {
        sendResponse(false, 'Site bulunamadı');
    }

    // Doğrudan contact_messages tablosuna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO contact_messages (site_code, full_name, email, phone, subject, message, created_at, status, message_type)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), 'new', 'contact')
    ");

    $fullMessage = "Konu: " . $subject . "\n\nMesaj: " . $message;

    $result = $stmt->execute(['metaanaliz-musavirlik', $name, $email, $phone, $subject, $fullMessage]);

    if ($result) {
        $contactId = $pdo->lastInsertId();

        // Bildirim oluştur
        $notificationTitle = "Yeni İletişim Talebi - " . SITE_NAME;
        $notificationMessage = "Yeni iletişim talebi: {$name} ({$email}) - {$subject}";

        createNotification($pdo, $siteId, 'contact', $contactId, $notificationTitle, $notificationMessage);

        // Log kaydı
        logRequest('contact_form', [
            'id' => $contactId,
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'subject' => $subject,
            'site_code' => SITE_CODE
        ]);

        sendResponse(true, 'İletişim formunuz başarıyla gönderildi. En kısa sürede size dönüş yapacağız.', [
            'contact_id' => $contactId
        ]);
    } else {
        sendResponse(false, 'Veriler kaydedilirken bir hata oluştu');
    }
} catch (Exception $e) {
    error_log("Contact form error: " . $e->getMessage());
    sendResponse(false, 'Bir hata oluştu. Lütfen tekrar deneyin.');
}