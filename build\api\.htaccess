# Meta Analiz Müşavirlik API .htaccess

# CORS Headers
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Rate Limiting (basit)
<RequireAll>
    Require all granted
</RequireAll>

# Dosya erişim kısıtlamaları
<Files "config.php">
    Require all denied
</Files>

<FilesMatch "\.(log|json)$">
    Require all denied
</FilesMatch>

# Data ve logs klasörlerini koru
<DirectoryMatch "^.*(data|logs).*$">
    Require all denied
</DirectoryMatch>

# PHP hata gösterimini kapat (production için)
php_flag display_errors Off
php_flag log_errors On

# Dosya yükleme limitleri
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 30
php_value max_input_time 30

# Gzip sıkıştırma
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache kontrol
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/json "access plus 0 seconds"
    ExpiresByType text/plain "access plus 0 seconds"
</IfModule>

# URL Rewriting
RewriteEngine On

# API endpoint'leri için clean URLs
RewriteRule ^contact/?$ contact.php [L,QSA]
RewriteRule ^meeting/?$ meeting.php [L,QSA]
RewriteRule ^test/?$ test.php [L,QSA]

# OPTIONS request'leri için
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
